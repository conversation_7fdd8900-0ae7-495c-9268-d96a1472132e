# App Theme
Theme: **Kavia AI Multi-Tenant Chatbot** - Modern, mobile-first design with brand orange (#f26a1b) primary color, using Shadcn/ui components with Tailwind CSS. Features light/dark theme support with radial gradient backgrounds and professional typography using Inter font.

# Components

## Core Layout Components
- **Component Name**: TenantProvider
  - **Use Case**: Provides tenant context throughout the application, manages multi-tenant state and API URL generation

- **Component Name**: SidebarProvider
  - **Use Case**: Manages sidebar state (expanded/collapsed), active items, and navigation context

- **Component Name**: SidebarLayout
  - **Use Case**: Main layout wrapper that combines sidebar and main content areas with responsive behavior

- **Component Name**: ThemeProvider
  - **Use Case**: Manages light/dark theme switching with system preference detection and localStorage persistence

## Sidebar Components
- **Component Name**: UnifiedSidebar
  - **Use Case**: Main sidebar component that switches between expanded and collapsed states based on screen size

- **Component Name**: ExpandedSidebar
  - **Use Case**: Full-width sidebar with navigation sections, user profile, and theme controls

- **Component Name**: CollapsedSidebar
  - **Use Case**: Compact sidebar with icon-only navigation and tooltip support for mobile/small screens

- **Component Name**: SidebarHeader
  - **Use Case**: Sidebar header with logo, navigation controls, and new chat button

- **Component Name**: SidebarFooter
  - **Use Case**: User profile dropdown, settings access, and theme toggle controls

- **Component Name**: SidebarContent
  - **Use Case**: Scrollable content area with animated sections for chat history and projects

## Content Components
- **Component Name**: MainContent
  - **Use Case**: Central content router that renders different views (new chat, chat history, projects, chat view) based on sidebar state

- **Component Name**: NewChat
  - **Use Case**: Landing page component with chat input for starting new conversations

- **Component Name**: ChatView
  - **Use Case**: Main chat interface with message thread, thinking indicators, and inline composer

- **Component Name**: ChatHistory
  - **Use Case**: List view of previous chat conversations with search and navigation capabilities

- **Component Name**: Projects
  - **Use Case**: Grid/list view of available projects with creation and navigation options

- **Component Name**: ProjectChatHistory
  - **Use Case**: Project-specific chat history with project metadata and navigation

## Chat Components
- **Component Name**: ChatInput
  - **Use Case**: Versatile chat input component with two variants (landing/inline) and file attachment support

- **Component Name**: ArtifactPanel
  - **Use Case**: Resizable side panel for displaying code artifacts, previews, and generated content

- **Component Name**: SyntaxHighlighter
  - **Use Case**: Code syntax highlighting component for displaying code blocks in chat messages

## UI Components (Shadcn/ui based)
- **Component Name**: Button
  - **Use Case**: Accessible button component with loading states, variants (default, destructive, outline, secondary, ghost, link), and block width support

- **Component Name**: IconButton
  - **Use Case**: Compact button for icon-only interactions with tooltip support

- **Component Name**: Avatar
  - **Use Case**: User profile images with fallback initials and various sizes

- **Component Name**: Card
  - **Use Case**: Container component for grouping related content with consistent styling

- **Component Name**: Dialog
  - **Use Case**: Modal dialogs for forms, confirmations, and detailed views

- **Component Name**: DropdownMenu
  - **Use Case**: Context menus and action menus with keyboard navigation support

- **Component Name**: Tooltip
  - **Use Case**: Contextual help text that appears on hover/focus

- **Component Name**: ScrollArea
  - **Use Case**: Custom scrollable areas with consistent styling across platforms

- **Component Name**: Separator
  - **Use Case**: Visual dividers between content sections

- **Component Name**: Tabs
  - **Use Case**: Tab navigation for organizing related content

- **Component Name**: Input
  - **Use Case**: Form input fields with validation states and consistent styling

- **Component Name**: Textarea
  - **Use Case**: Multi-line text input with auto-resize capabilities

- **Component Name**: Select
  - **Use Case**: Dropdown selection component with search and keyboard navigation

- **Component Name**: Checkbox
  - **Use Case**: Boolean input controls with indeterminate state support

- **Component Name**: RadioGroup
  - **Use Case**: Single-selection input groups with accessible keyboard navigation

- **Component Name**: Switch
  - **Use Case**: Toggle controls for boolean settings (like theme switching)

- **Component Name**: Slider
  - **Use Case**: Range input controls for numeric values

- **Component Name**: Progress
  - **Use Case**: Progress indicators for loading states and completion tracking

- **Component Name**: Badge
  - **Use Case**: Status indicators and labels with variant styling

- **Component Name**: Alert
  - **Use Case**: Notification messages with different severity levels

- **Component Name**: Spinner
  - **Use Case**: Loading indicators with various sizes for different contexts

## Utility Components
- **Component Name**: HydrationFix
  - **Use Case**: Prevents hydration mismatches in SSR environments

- **Component Name**: ThemeToggle
  - **Use Case**: Theme switching control with system/light/dark options

## Form Components
- **Component Name**: FormField
  - **Use Case**: Wrapper for form inputs with label, validation, and error handling

- **Component Name**: FieldMessage
  - **Use Case**: Displays validation messages and field descriptions
