# Chat Interface Feature

## Overview
Real-time AI chat interface with message threading, artifact panel, and responsive design optimized for both desktop and mobile experiences.

## Route
- **Path**: `/{tenant}/{project}/chat` or `/{tenant}/newchat`
- **Component**: `src/components/content/chat-view.tsx`

## Specifications

### Layout Structure
- **Chat Area**: Dynamic width based on artifact panel state
- **Artifact Panel**: Resizable side panel (300-800px width)
- **Message Container**: Max width 768px (max-w-3xl) centered
- **Input Area**: Sticky bottom positioning with backdrop blur

### Message Display
- **User Messages**: Right-aligned with user avatar
- **Assistant Messages**: Left-aligned with AI avatar
- **Message Spacing**: 24px between messages (space-y-6)
- **Auto-scroll**: Automatically scrolls to latest message
- **Thinking Indicator**: Spinner with "Thinking..." text during AI processing

### Chat Input Component
- **Variants**:
  - `landing`: Full-featured input for new chat page
  - `inline`: Compact input for chat view
- **Sizes**:
  - `md`: Standard size for landing page
  - `sm`: Compact size for inline usage
- **Features**:
  - Auto-resize textarea (1-6 lines)
  - File attachment support
  - Paste handling for files
  - Character counter (8000 max)
  - Send button with loading states

### Artifact Panel
- **Trigger**: Automatically opens when artifacts are created
- **Content Types**: Code, previews, documents
- **Resizing**: Drag handle for width adjustment
- **Responsive**: Collapses on mobile devices
- **Minimum Width**: 300px desktop, full-width mobile

### State Management (Zustand)
- **Messages**: Array of chat messages with roles and content
- **Status**: Chat status (idle, thinking, error)
- **Artifacts**: Generated code/content artifacts
- **Panel State**: Artifact panel open/closed and width
- **Actions**: sendMessage, simulateAssistantResponse, createArtifact

### Responsive Design
- **Desktop**: Side-by-side chat and artifact panel
- **Mobile**: Full-width chat with overlay artifact panel
- **Breakpoints**: Uses Tailwind responsive prefixes (sm:, md:, lg:)
- **Touch Targets**: 44px minimum for mobile interactions

### Styling Classes
- **Chat Container**: `flex h-full bg-white`
- **Message Area**: `overflow-y-auto flex-1`
- **User Message**: Right-aligned with brand styling
- **Assistant Message**: Left-aligned with neutral styling
- **Input Container**: `sticky bottom-0 z-10 bg-white/80 backdrop-blur`

### Animation & Polish
- **Smooth Transitions**: 300ms ease-out for panel state changes
- **Gradient Overlay**: Bottom gradient for visual polish
- **Loading States**: Spinner animations during processing
- **Auto-scroll**: Smooth scrolling to new messages

### Integration Points
- **Tenant Context**: Multi-tenant aware routing and API calls
- **Project Context**: Project-specific chat history and settings
- **Theme System**: Supports light/dark theme switching
- **Sidebar**: Integrates with sidebar navigation state

### Performance Optimizations
- **Virtual Scrolling**: For large message histories
- **Lazy Loading**: Messages loaded on demand
- **Debounced Input**: Prevents excessive API calls
- **Memoized Components**: React.memo for message components

### Accessibility
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Proper ARIA labels and roles
- **Focus Management**: Logical tab order
- **Color Contrast**: WCAG AA compliant colors

### Future Enhancements
- Message editing and deletion
- Message reactions and threading
- Voice input support
- Message search functionality
- Export chat history
- Collaborative chat features
