# Login Page Feature

## Overview
Tenant-specific authentication page that provides a clean, centered login form with theme support.

## Route
- **Path**: `/{tenant}/login`
- **Component**: `src/app/[tenant]/login/page.tsx`

## Specifications

### Layout
- **Container**: Full-screen centered layout with background theme support
- **Form Container**: Card-style container with border and background blur
- **Max Width**: 400px (max-w-md)
- **Positioning**: Centered both horizontally and vertically

### Form Fields
- **Email Field**:
  - Type: email
  - Placeholder: "Enter your email"
  - Validation: HTML5 email validation
  - Styling: Consistent with theme input styling

- **Password Field**:
  - Type: password
  - Placeholder: "Enter your password"
  - Styling: Consistent with theme input styling

### UI Elements
- **Title**: Dynamic tenant name display ("Welcome to {tenant}")
- **Subtitle**: "Sign in to access your Kavia chatbot"
- **Theme Toggle**: Positioned in top-right corner
- **Submit Button**: Full-width primary button with hover states

### Styling Classes
- **Background**: `bg-background` with theme-aware colors
- **Form Container**: `border-border/60 bg-card/60 rounded-xl border p-6`
- **Input Fields**: `text-foreground placeholder:text-muted-foreground border-input bg-background`
- **Button**: `border-primary/20 bg-primary/10 text-primary hover:bg-primary/20`

### Theme Support
- **Light Theme**: Clean white background with subtle borders
- **Dark Theme**: Dark background with appropriate contrast
- **System Theme**: Automatically follows user's system preference

### Accessibility
- **Labels**: Proper form labels for screen readers
- **Focus States**: Keyboard navigation support
- **Color Contrast**: WCAG compliant color combinations

### Integration
- **Tenant Context**: Dynamically displays tenant name from URL parameter
- **Theme Provider**: Integrates with global theme system
- **Navigation**: Part of tenant-specific routing structure

### Future Enhancements
- Form validation with error states
- Loading states during authentication
- Social login options
- Password reset functionality
- Remember me checkbox
- Multi-factor authentication support
