# Theme System Feature

## Overview
Comprehensive theming system supporting light, dark, and system themes with brand-consistent colors, smooth transitions, and accessibility compliance.

## Components
- **ThemeProvider**: `src/components/theme/theme-provider.tsx`
- **ThemeToggle**: `src/components/theme/theme-toggle.tsx`
- **Global Styles**: `src/app/globals.css`

## Specifications

### Theme Options
- **Light Theme**: Clean, bright interface with white backgrounds
- **Dark Theme**: Dark interface optimized for low-light environments
- **System Theme**: Automatically follows user's OS preference

### Color System (HSL Values)

#### Brand Colors
- **Primary**: Orange (#f26a1b) - Brand color for CTAs and highlights
- **Primary Foreground**: White text on primary backgrounds
- **Secondary**: Neutral gray for secondary actions
- **Accent**: Subtle accent color for hover states

#### Semantic Colors
- **Success**: Green (#22c55e) for positive actions and states
- **Warning**: Amber (#f59e0b) for caution and alerts
- **Destructive**: Red (#ef4444) for errors and dangerous actions
- **Muted**: Subtle gray for secondary text and backgrounds

#### Layout Colors
- **Background**: Main page background with subtle gradients
- **Foreground**: Primary text color with high contrast
- **Card**: Container backgrounds with subtle elevation
- **Border**: Consistent border colors throughout UI
- **Input**: Form input backgrounds and borders

### CSS Custom Properties
```css
:root {
  --primary: 24 95% 53%;           /* Brand orange */
  --background: 0 0% 100%;         /* White background */
  --foreground: 222.2 47.4% 11.2%; /* Dark text */
  --radius: 0.5rem;               /* Border radius */
}

.dark {
  --primary: 28 96% 61%;           /* Lighter orange for dark */
  --background: 24 14% 10%;        /* Dark background */
  --foreground: 0 0% 98%;          /* Light text */
}
```

### Theme Provider Implementation
```typescript
interface ThemeContextValue {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark';
}
```

### Theme Persistence
- **Storage Key**: `kavia-theme` in localStorage
- **SSR Handling**: Prevents hydration mismatches
- **Initial Load**: Script injection for immediate theme application
- **System Detection**: Media query for system preference

### Background Gradients
- **Light Theme**: Subtle white to warm white gradient
- **Dark Theme**: Dark gradient with brand color accents
- **Radial Patterns**: Decorative radial gradients for visual interest
- **Fixed Attachment**: Background stays fixed during scroll

### Theme Toggle Component
- **Three States**: Light, dark, system options
- **Icon Indicators**: Sun, moon, and monitor icons
- **Smooth Transitions**: Animated state changes
- **Accessibility**: Keyboard navigation and screen reader support

### Tailwind Integration
- **CSS Variables**: Tailwind configured to use CSS custom properties
- **Dark Mode**: Class-based dark mode strategy
- **Color Palette**: Extended with brand and semantic colors
- **Responsive**: Theme-aware responsive design

### Animation Support
- **Transition Classes**: Smooth color transitions
- **Reduced Motion**: Respects user's motion preferences
- **Theme Switching**: Smooth transitions between themes
- **Component Animations**: Theme-aware animation colors

### Typography System
- **Font Family**: Inter for primary text, JetBrains Mono for code
- **Font Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)
- **Line Heights**: Optimized for readability across themes
- **Font Features**: OpenType features enabled (rlig, calt)

### Accessibility Compliance
- **WCAG AA**: Minimum 4.5:1 contrast ratio for normal text
- **WCAG AAA**: 7:1 contrast ratio for important text
- **Color Independence**: Information not conveyed by color alone
- **Focus Indicators**: High contrast focus states
- **Reduced Motion**: Respects prefers-reduced-motion

### Component Integration
- **Shadcn/ui**: All components inherit theme colors
- **Custom Components**: Use CSS custom properties
- **Icon Colors**: Theme-aware icon coloring
- **State Colors**: Consistent hover, active, and focus states

### Development Features
- **Hot Reloading**: Theme changes apply immediately
- **CSS Variables**: Easy customization and debugging
- **Design Tokens**: Centralized color management
- **Theme Preview**: Easy theme switching for development

### Performance Optimizations
- **CSS-in-CSS**: No runtime CSS generation
- **Minimal JavaScript**: Theme switching with minimal JS
- **Cached Preferences**: Theme preference cached locally
- **Efficient Selectors**: Optimized CSS selectors

### Browser Support
- **Modern Browsers**: Full support for CSS custom properties
- **Fallbacks**: Graceful degradation for older browsers
- **Progressive Enhancement**: Enhanced experience for capable browsers
- **Mobile Support**: Optimized for mobile browsers

### Customization Options
- **Brand Colors**: Easy brand color customization
- **Radius Values**: Configurable border radius
- **Spacing Scale**: Consistent spacing system
- **Shadow System**: Elevation-based shadow system

### Integration Points
- **Next.js**: SSR-compatible theme system
- **Tailwind CSS**: Deep integration with Tailwind
- **Framer Motion**: Theme-aware animations
- **Radix UI**: Theme colors applied to Radix components

### Future Enhancements
- **High Contrast Mode**: Enhanced accessibility theme
- **Custom Themes**: User-defined color schemes
- **Tenant Themes**: Per-tenant theme customization
- **Seasonal Themes**: Time-based theme variations
- **Color Blind Support**: Enhanced color accessibility
- **Theme Marketplace**: Downloadable theme packages
- **Advanced Customization**: Visual theme editor
- **Theme Analytics**: Usage tracking for theme preferences
