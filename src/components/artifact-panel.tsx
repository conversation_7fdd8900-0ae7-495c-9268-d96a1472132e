'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import { useChatStore } from '@/store/chat';
import SyntaxHighlighter from '@/components/content/syntax-highlighter';
import {
  X,
  Copy,
  Download,
  Maximize2,
  Minimize2,
  Code2,
  Eye,
  BookOpen,
  Check,
  GripVertical
} from 'lucide-react';
import { useArtifactPanel } from '@/store/components';

type TabType = 'code' | 'preview' | 'reference';

export default function ArtifactPanel() {
  const { 
    artifactPanelOpen, 
    setArtifactPanelOpen, 
    artifactPanelWidth,
    setArtifactPanelWidth,
    getActiveArtifact 
  } = useChatStore();
  
  const { activeTab, isMaximized, isResizing, copiedStates, setTab, setMaximized, setResizing, setCopied } = useArtifactPanel();

  const resizeRef = useRef<HTMLDivElement>(null);
  const [isDesktop, setIsDesktop] = useState(false);
  
  // Panel width constraints (in pixels)
  const MIN_WIDTH = 320; // Minimum usable width
  const [maxWidth, setMaxWidth] = useState(960);
  
  // Update max width and desktop detection
  useEffect(() => {
    const updateScreenInfo = () => {
      if (typeof window !== 'undefined') {
        // Ensure chat has at least 400px width for proper content display
        const chatMinWidth = 400; // Minimum chat width + padding
        const maxPanelWidth = Math.max(320, window.innerWidth - chatMinWidth); // Panel can't be smaller than 320px
        setMaxWidth(Math.min(window.innerWidth * 0.7, maxPanelWidth)); // Reduced from 75% to 70%
        setIsDesktop(window.innerWidth >= 768);
      }
    };
    
    updateScreenInfo();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateScreenInfo);
      return () => window.removeEventListener('resize', updateScreenInfo);
    }
    return undefined;
  }, []);

  const artifact = getActiveArtifact();

  // Handle resize start
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setResizing(true);

    const startX = e.clientX;
    const startWidth = artifactPanelWidth;

    const handleResize = (e: MouseEvent) => {
      const deltaX = startX - e.clientX; // Dragging left increases width
      const newWidth = Math.min(maxWidth, Math.max(MIN_WIDTH, startWidth + deltaX));
      setArtifactPanelWidth(newWidth);
    };

    const handleResizeEnd = () => {
      setResizing(false);
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleResizeEnd);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
    
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', handleResizeEnd);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [artifactPanelWidth, setArtifactPanelWidth, maxWidth]);

  // Constrain panel width when window resizes
  useEffect(() => {
    if (artifactPanelWidth > maxWidth) {
      setArtifactPanelWidth(maxWidth);
    }
  }, [maxWidth, artifactPanelWidth, setArtifactPanelWidth]);

  // Early return after all hooks are called
  if (!artifactPanelOpen || !artifact) return null;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content);
      setCopied('main', true);
      setTimeout(() => setCopied('main', false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const handleDownload = () => {
    const element = document.createElement('a');
    const file = new Blob([artifact.content], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    
    const extension = getFileExtension(artifact.type);
    element.download = `${artifact.title.toLowerCase().replace(/\s+/g, '-')}.${extension}`;
    
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const getFileExtension = (type: string): string => {
    switch (type) {
      case 'react':
      case 'typescript':
        return 'tsx';
      case 'javascript':
        return 'js';
      case 'html':
        return 'html';
      case 'python':
        return 'py';
      default:
        return 'txt';
    }
  };

  const handleClose = () => {
    setArtifactPanelOpen(false);
    setMaximized(false);
  };

  const toggleMaximize = () => {
    setMaximized(!isMaximized);
  };

  const tabs = [
    { id: 'code' as TabType, label: 'Code', icon: Code2 },
    { id: 'preview' as TabType, label: 'Preview', icon: Eye },
    { id: 'reference' as TabType, label: 'Reference', icon: BookOpen },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'code':
        return (
          <div className="overflow-auto flex-1">
            <SyntaxHighlighter
              code={artifact.content}
              language={artifact.language}
              showLineNumbers={true}
              className="h-auto"
            />
          </div>
        );
      
      case 'preview':
        if (artifact.type === 'html') {
          return (
            <div className="overflow-hidden flex-1 bg-white" style={{ minHeight: 0 }}>
              <iframe
                srcDoc={artifact.content}
                className="w-full h-full border-0"
                sandbox="allow-scripts allow-same-origin"
                title="Preview"
                style={{ 
                  height: '100%', 
                  minHeight: '400px',
                  maxHeight: '100%',
                  pointerEvents: isResizing ? 'none' : 'auto' // Disable pointer events during resize
                }}
              />
            </div>
          );
        } else if (artifact.type === 'react') {
          return (
            <div className="flex flex-1 justify-center items-center bg-neutral-50">
              <div className="text-center text-neutral-600">
                <Eye className="mx-auto mb-4 w-12 h-12 text-neutral-400" />
                <h3 className="mb-2 text-lg font-medium">React Component Preview</h3>
                <p className="text-sm">
                  Live preview not available for React components.
                  <br />
                  Copy the code to test in your environment.
                </p>
              </div>
            </div>
          );
        }
        return (
          <div className="flex flex-1 justify-center items-center bg-neutral-50">
            <div className="text-center text-neutral-600">
              <Eye className="mx-auto mb-4 w-12 h-12 text-neutral-400" />
              <h3 className="mb-2 text-lg font-medium">Preview Not Available</h3>
              <p className="text-sm">Preview is only available for HTML content.</p>
            </div>
          </div>
        );
      
      case 'reference':
        return (
          <div className="overflow-auto flex-1 bg-white">
            <div className="p-6">
              <div className="max-w-2xl">
                <h2 className="mb-4 text-xl font-semibold text-neutral-900">
                  {artifact.title}
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="mb-2 text-sm font-medium text-neutral-700">Type</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      {artifact.type}
                    </span>
                  </div>
                  
                  <div>
                    <h3 className="mb-2 text-sm font-medium text-neutral-700">Language</h3>
                    <p className="text-sm text-neutral-600">{artifact.language}</p>
                  </div>
                  
                  <div>
                    <h3 className="mb-2 text-sm font-medium text-neutral-700">Created</h3>
                    <p className="text-sm text-neutral-600">
                      {artifact.createdAt.toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="mb-2 text-sm font-medium text-neutral-700">Description</h3>
                    <p className="text-sm text-neutral-600">
                      This {artifact.type} artifact contains {artifact.content.split('\n').length} lines of code.
                      {artifact.type === 'react' && ' It\'s a React component that can be integrated into your project.'}
                      {artifact.type === 'html' && ' It\'s an HTML document that can be opened in any web browser.'}
                      {artifact.type === 'python' && ' It\'s a Python script that can be executed with a Python interpreter.'}
                    </p>
                  </div>
                  
                  {artifact.type === 'react' && (
                    <div>
                      <h3 className="mb-2 text-sm font-medium text-neutral-700">Usage</h3>
                      <div className="p-3 text-sm rounded-lg bg-neutral-50 text-neutral-600">
                        <p>1. Copy the component code</p>
                        <p>2. Save it as a .tsx file in your React project</p>
                        <p>3. Import and use it in your application</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <>
      {/* Mobile backdrop - only show on mobile */}
      <div 
        className="fixed inset-0 z-40 backdrop-blur-sm bg-black/50 md:hidden"
        onClick={handleClose}
      />
      
      {/* Panel */}
      <div 
        ref={resizeRef}
        className={`
          fixed right-0 top-0 h-screen bg-white border-l border-neutral-200 z-50 flex flex-col
          transition-transform duration-300 ease-out
          ${isMaximized ? 'w-full' : 'w-full md:block'}
          ${artifactPanelOpen ? 'translate-x-0' : 'translate-x-full'}
          ${isResizing ? 'transition-none' : ''}`}
        style={{
          width: isMaximized ? '100%' : (isDesktop ? `${artifactPanelWidth}px` : '100%'),
          height: '100vh' // Explicit viewport height
        }}
      >
        {/* Resize Handle */}
        {!isMaximized && isDesktop && (
          <div
            className={`
              absolute left-0 top-0 bottom-0 w-1 bg-transparent hover:bg-orange-100 cursor-col-resize z-50
              flex items-center justify-center group transition-colors
              ${isResizing ? 'bg-orange-200' : ''}`}
            onMouseDown={handleResizeStart}
          >
            <div className="flex justify-center items-center w-4 h-8 rounded-full opacity-0 transition-colors bg-neutral-300 group-hover:bg-orange-400 group-hover:opacity-100">
              <GripVertical className="w-3 h-3 text-white" />
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex flex-shrink-0 justify-between items-center px-4 h-14 border-b border-neutral-200 bg-neutral-50">
          <div className="flex items-center space-x-3">
            <h2 className="font-medium truncate text-neutral-900">
              {artifact.title}
            </h2>
            <span className="px-2 py-1 text-xs rounded text-neutral-500 bg-neutral-100">
              {artifact.type}
            </span>
          </div>
          
          <div className="flex items-center space-x-1">
            {/* Copy button */}
            <button
              onClick={handleCopy}
              className="p-2 rounded-md transition-colors text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200"
              title="Copy code"
            >
              {copySuccess ? (
                <Check className="w-4 h-4 text-green-600" />
              ) : (
                <Copy className="w-4 h-4" />
              )}
            </button>
            
            {/* Download button */}
            <button
              onClick={handleDownload}
              className="p-2 rounded-md transition-colors text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200"
              title="Download file"
            >
              <Download className="w-4 h-4" />
            </button>
            
            {/* Maximize/Minimize button */}
            <button
              onClick={toggleMaximize}
              className="hidden p-2 rounded-md transition-colors text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200 md:block"
              title={isMaximized ? 'Restore' : 'Maximize'}
            >
              {isMaximized ? (
                <Minimize2 className="w-4 h-4" />
              ) : (
                <Maximize2 className="w-4 h-4" />
              )}
            </button>
            
            {/* Close button */}
            <button
              onClick={handleClose}
              className="p-2 rounded-md transition-colors text-neutral-600 hover:text-neutral-900 hover:bg-neutral-200"
              title="Close panel"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="flex flex-shrink-0 bg-white border-b border-neutral-200">
          {tabs.map(tab => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setTab(tab.id)}
                className={`
                  flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors relative
                  ${isActive 
                    ? 'text-orange-600 bg-orange-50' 
                    : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-50'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
                
                {isActive && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-orange-500" />
                )}
              </button>
            );
          })}
        </div>
        
        {/* Content */}
        <div 
          className="flex overflow-hidden relative flex-col flex-1" 
          style={{ 
            minHeight: 0,
            height: 'calc(100vh - 108px)' // Subtract header (56px) and tabs (52px) height
          }}
        >
          {renderTabContent()}
          
          {/* Resize overlay - prevents iframe from capturing mouse events during resize */}
          {isResizing && (
            <div className="absolute inset-0 z-50 bg-transparent cursor-col-resize" />
          )}
        </div>
      </div>
    </>
  );
}