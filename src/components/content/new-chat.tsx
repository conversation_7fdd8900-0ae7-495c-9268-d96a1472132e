'use client';

import React from 'react';
import Image from 'next/image';
import ChatInput from './chat-input';
import { useChatStore } from '@/store/chat';
import { useSidebar } from '@/components/sidebar/use-sidebar';
import { useChatInputForm } from '@/store/forms';

export function NewChat() {
  const { value: inputValue, attachmentCount, setValue: setInputValue, setAttachmentCount } = useChatInputForm();

  // Chat state + navigation to chat interface
  const startNewChat = useChatStore((s) => s.startNewChat);
  const simulateAssistantResponse = useChatStore((s) => s.simulateAssistantResponse);
  const { setActiveItem } = useSidebar();


  const recommendations = [
    {
      emoji: '🚀',
      title: 'Start New Project',
      description: 'Create something amazing',
      category: 'project'
    },
    {
      emoji: '💡',
      title: 'Code Review',
      description: 'Improve your code',
      category: 'code'
    },
    {
      emoji: '🎨',
      title: 'Design Help',
      description: 'Build beautiful interfaces',
      category: 'design'
    },
    {
      emoji: '🔧',
      title: 'Debug Issues',
      description: 'Fix bugs quickly',
      category: 'debug'
    }
  ];

  const baseTop = attachmentCount > 0 ? 527 : 445;
  const recommendationsToRender = recommendations.slice(0, 4);
  const renderedCount = recommendationsToRender.length;

  // Handle recommendation click
  const handleRecommendationClick = (category: string, title: string) => {
    const prompts = {
      project: "I want to start a new project. Can you help me set up the structure and choose the right technologies?",
      code: "I need help reviewing and optimizing my code. Can you help me identify improvements and best practices?",
      design: "I want to create a modern UI/UX design. Can you help me with design patterns and component libraries?",
      data: "I have data that I need to analyze. Can you help me extract insights and create visualizations?",
      debug: "I'm facing some bugs and performance issues in my code. Can you help me troubleshoot and fix them?",
      learn: "I want to learn a new technology or framework. Can you provide me with a structured learning path?"
    };
    
    const prompt = prompts[category as keyof typeof prompts] || `Help me with ${title.toLowerCase()}`;
    startNewChat(prompt);
    setActiveItem('chat');
    setInputValue('');
    simulateAssistantResponse(`I'd be happy to help you with ${title.toLowerCase()}! Let's get started.`, 900);
  };

  return (
    <div className="overflow-hidden relative min-h-screen bg-white">
      {/* Background blur ellipse - exact position from Figma */}
      <div 
        className="absolute"
        style={{
          left: '1003px',
          top: '-240px',
          width: '446px',
          height: '302px',
          background: 'rgba(242, 106, 27, 0.4)',
          borderRadius: '50%',
          filter: 'blur(240px)',
          transform: 'translateX(-50%)'
        }}
      />
      
      {/* Logo - exact position from Figma */}
      <div 
        className="flex absolute justify-center items-center"
        style={{
          left: '50%',
          top: '174px',
          transform: 'translateX(-50%)',
          width: '119px',
          height: '36px'
        }}
      >
        <div className="flex items-center">
          <Image src="/logo.svg" alt="Kavia logo" width={137.034} height={36} />
        </div>
      </div>

      <ChatInput
        value={inputValue}
        onChange={setInputValue}
        onSend={(val) => {
          startNewChat(val);
          setActiveItem('chat');
          setInputValue('');
          // Demo: show immediate thinking and a friendly first reply
          simulateAssistantResponse("Hey there! How's it going? What can I help you with today?", 900);
        }}
        onFilesChange={setAttachmentCount}
      />

      {/* Recommendations section title */}
      <p 
        className="absolute text-center"
        style={{
          left: '50%',
          top: attachmentCount > 0 ? '488px' : '406px',
          transition: 'top 250ms ease',
          transform: 'translateX(-50%)',
          width: '592px',
          color: 'rgba(25, 22, 22, 0.7)',
          fontSize: '14px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: '400',
          lineHeight: '23px',
          margin: 0
        }}
      >
        What would you like to work on today?
      </p>

      {/* Simple recommendation cards */}
      {recommendationsToRender.map((recommendation, index) => (
        <div
          key={index}
          className="absolute transition-opacity cursor-pointer hover:opacity-80"
          onClick={() => handleRecommendationClick(recommendation.category, recommendation.title)}
          style={{
            left: '50%',
            top: `${baseTop + (index * 64)}px`,
            transition: 'top 250ms ease',
            transform: 'translateX(-50%)',
            width: '616px',
            height: '48px',
            display: 'flex',
            alignItems: 'center',
            gap: '16px'
          }}
        >
          {/* Emoji container */}
          <div 
            style={{
              width: '48px',
              height: '48px',
              background: '#F7F8F9',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '18px',
              fontFamily: 'Inter, sans-serif',
              lineHeight: '27px'
            }}
          >
            {recommendation.emoji}
          </div>

          {/* Content */}
          <div style={{ flex: 1, height: '43px', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            {/* Title */}
            <div style={{ height: '23px', display: 'flex', alignItems: 'center' }}>
              <span 
                style={{
                  color: '#191616',
                  fontSize: '14px',
                  fontFamily: 'Inter, sans-serif',
                  fontWeight: '500',
                  lineHeight: '23px'
                }}
              >
                {recommendation.title}
              </span>
            </div>

            {/* Description */}
            <div style={{ height: '20px', display: 'flex', alignItems: 'center' }}>
              <span 
                style={{
                  color: 'rgba(25, 22, 22, 0.8)',
                  fontSize: '12px',
                  fontFamily: 'Inter, sans-serif',
                  fontWeight: '400',
                  lineHeight: '20px'
                }}
              >
                {recommendation.description}
              </span>
            </div>
          </div>
        </div>
      ))}


      {/* Bottom gradient overlay - exact specs from Figma */}
      <div 
        className="fixed bottom-0"
        style={{
          left: '60px',
          width: 'calc(100vw - 120px)',
          maxWidth: '1380px',
          height: '131px',
          background: 'linear-gradient(0deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0.45) 100%)',
          pointerEvents: 'none',
          marginLeft: '50%',
          transform: 'translateX(-50%)'
        }}
      />

      {/* ChatInput includes hidden textarea and focus overlay */}

      {/* Mobile responsive adjustments */}
      <style jsx>{`
        @media (max-width: 768px) {
          .absolute[style*="width: 616px"] {
            width: calc(100vw - 40px) !important;
            left: 20px !important;
            transform: none !important;
          }
          
          .absolute[style*="width: 592px"] {
            width: calc(100vw - 60px) !important;
            left: 30px !important;
            transform: none !important;
          }
        }
        
        @media (max-width: 480px) {
          .absolute[style*="top: 174px"] {
            top: 100px !important;
          }
          
          .absolute[style*="top: 406px"] {
            top: 320px !important;
          }
          
          .absolute[style*="top: 445px"] {
            top: 360px !important;
          }
        }
      `}</style>
    </div>
  );
}