'use client';

import React, { useEffect, useRef } from 'react';
import { useChatStore } from '@/store/chat';
import ChatInput from './chat-input';
import { Spinner } from '@/components/ui/spinner';
import ArtifactPanel from '../artifact-panel';
import Image from 'next/image';
import { Code2, Eye, FileText } from 'lucide-react';
import { useChatViewComponent } from '@/store/components';

interface ChatViewProps {
  chatId?: string;
  projectId?: string;
}

export default function ChatView({ chatId, projectId }: ChatViewProps = {}) {
  const messages = useChatStore((s) => s.messages);
  const status = useChatStore((s) => s.status);
  const sendMessage = useChatStore((s) => s.sendMessage);
  const simulateAssistantResponse = useChatStore((s) => s.simulateAssistantResponse);
  const artifactPanelOpen = useChatStore((s) => s.artifactPanelOpen);
  const artifactPanelWidth = useChatStore((s) => s.artifactPanelWidth);
  const artifacts = useChatStore((s) => s.artifacts);
  const createArtifact = useChatStore((s) => s.createArtifact);
  const setActiveArtifact = useChatStore((s) => s.setActiveArtifact);
  const setArtifactPanelOpen = useChatStore((s) => s.setArtifactPanelOpen);

  const { input, isDesktop, setInput, setDesktop } = useChatViewComponent();
  const listRef = useRef<HTMLDivElement | null>(null);

  // Update desktop detection
  useEffect(() => {
    const updateDesktopDetection = () => {
      if (typeof window !== 'undefined') {
        setDesktop(window.innerWidth >= 768);
      }
    };

    updateDesktopDetection();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateDesktopDetection);
      return () => window.removeEventListener('resize', updateDesktopDetection);
    }
    return undefined;
  }, [setDesktop]);

  // Auto-scroll to bottom on new messages or when thinking
  useEffect(() => {
    const el = listRef.current;
    if (!el) return;
    // smooth scroll to bottom
    el.scrollTo({ top: el.scrollHeight, behavior: 'smooth' });
  }, [messages.length, status]);

  // Demo artifact creation logic
  const createDemoArtifact = (userMessage: string): string | undefined => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('component') || lowerMessage.includes('react')) {
      return createArtifact({
        title: 'ProductCard Component',
        type: 'react',
        language: 'typescript',
        content: `import React from 'react';

interface ProductCardProps {
  title: string;
  price: number;
  image: string;
  description: string;
  onAddToCart: () => void;
}

export default function ProductCard({ 
  title, 
  price, 
  image, 
  description, 
  onAddToCart 
}: ProductCardProps) {
  return (
    <div className="overflow-hidden max-w-sm bg-white rounded-lg border border-gray-200 shadow-md">
      <img 
        src={image} 
        alt={title}
        className="object-cover w-full h-48"
      />
      
      <div className="p-5">
        <h5 className="mb-2 text-2xl font-bold tracking-tight text-gray-900">
          {title}
        </h5>
        
        <p className="mb-3 font-normal text-gray-700">
          {description}
        </p>
        
        <div className="flex justify-between items-center">
          <span className="text-3xl font-bold text-gray-900">
            $99.99
          </span>
          
          <button
            onClick={onAddToCart}
            className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
          >
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
}`
      });
    }
    
    if (lowerMessage.includes('html') || lowerMessage.includes('page')) {
      return createArtifact({
        title: 'Landing Page',
        type: 'html',
        language: 'html',
        content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Landing Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 2rem;
            text-align: center;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .cta-button {
            background: #ff6b6b;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }
        
        .features {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature {
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature h3 {
            margin-bottom: 1rem;
            color: #667eea;
        }
    </style>
</head>
<body>
    <section class="hero">
        <h1>Welcome to the Future</h1>
        <p>Experience the next generation of web development with our cutting-edge solutions.</p>
        <button class="cta-button" onclick="alert('Hello from the demo!')">Get Started</button>
    </section>
    
    <section class="features">
        <div class="feature">
            <h3>⚡ Lightning Fast</h3>
            <p>Built for speed and performance, delivering exceptional user experiences.</p>
        </div>
        
        <div class="feature">
            <h3>🎨 Beautiful Design</h3>
            <p>Stunning visuals and intuitive interfaces that users love.</p>
        </div>
        
        <div class="feature">
            <h3>🔒 Secure</h3>
            <p>Enterprise-grade security to protect your data and users.</p>
        </div>
    </section>
</body>
</html>`
      });
    }
    
    if (lowerMessage.includes('python')) {
      return createArtifact({
        title: 'Data Analysis Script',
        type: 'python',
        language: 'python',
        content: `import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta

def analyze_sales_data():
    """Analyze sales data and generate insights"""
    # Generate sample sales data
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    sales_data = {
        'date': dates,
        'sales': np.random.normal(1000, 200, len(dates)),
        'customers': np.random.poisson(50, len(dates)),
        'product_category': np.random.choice(['Electronics', 'Clothing', 'Books'], len(dates))
    }
    
    df = pd.DataFrame(sales_data)
    df['sales'] = df['sales'].clip(lower=0)
    
    # Basic analysis
    print("=== Sales Data Analysis ===")
    print("Total Sales:", df['sales'].sum())
    print("Average Daily Sales:", df['sales'].mean())
    print("Best Sales Day:", df['sales'].max())
    print("Total Customers:", df['customers'].sum())
    
    # Monthly analysis
    df['month'] = df['date'].dt.month
    monthly_sales = df.groupby('month')['sales'].sum()
    
    print("Monthly Sales Summary:")
    for month, sales in monthly_sales.items():
        print(f"Month {month}: {sales}")
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    plt.subplot(2, 2, 1)
    plt.plot(df['date'], df['sales'])
    plt.title('Daily Sales Trend')
    
    plt.tight_layout()
    plt.show()
    
    return df

if __name__ == "__main__":
    sales_df = analyze_sales_data()
    print("Analysis complete!")
`
      });
    }
    
    return undefined;
  };

  const handleSend = (text: string, files: File[]) => {
    const trimmed = text.trim();
    if (!trimmed) return;
    sendMessage(trimmed);
    setInput('');
    
    // Note: files parameter reserved for future file upload functionality
    console.log('Files received:', files.length);
    
    // Check if we should create a demo artifact
    const artifactId = createDemoArtifact(trimmed);
    
    // Demo: simulate assistant response
    if (artifactId) {
      simulateAssistantResponse(
        'I\'ve created a sample artifact for you! You can view the code, preview it (for HTML), and access the reference documentation in the panel on the right.',
        1000,
        artifactId
      );
    } else {
      simulateAssistantResponse(
        'Thanks for the message! I\'ll help you with that. Here is a thoughtful response based on your request. Try asking me to create a "React component", "HTML page", or "Python script" to see the artifact system in action!',
        1000
      );
    }
  };

  const isThinking = status === 'thinking';

  // Artifact card component
  const ArtifactCard = ({ artifactId }: { artifactId: string }) => {
    const artifact = artifacts.find(a => a.id === artifactId);
    if (!artifact) return null;

    const handleClick = () => {
      setActiveArtifact(artifactId);
      setArtifactPanelOpen(true);
    };

    const getIcon = () => {
      switch (artifact.type) {
        case 'react':
        case 'javascript':
        case 'typescript':
          return <Code2 className="w-4 h-4" />;
        case 'html':
          return <Eye className="w-4 h-4" />;
        case 'python':
          return <FileText className="w-4 h-4" />;
        default:
          return <Code2 className="w-4 h-4" />;
      }
    };

    return (
      <div 
        onClick={handleClick}
        className="p-4 mt-3 rounded-lg border transition-colors cursor-pointer bg-neutral-50 border-neutral-200 hover:bg-neutral-100 group"
      >
        <div className="flex items-start space-x-3">
          <div className="flex justify-center items-center w-8 h-8 text-orange-600 bg-orange-100 rounded-md">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center mb-1 space-x-2">
              <h4 className="text-sm font-medium truncate text-neutral-900">
                {artifact.title}
              </h4>
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                {artifact.type}
              </span>
            </div>
            
            <p className="mb-2 text-xs text-neutral-600">
              {artifact.content.split('\n').length} lines • {artifact.language}
            </p>
            
            <div className="flex items-center space-x-1 text-xs text-orange-600 group-hover:text-orange-700">
              <Eye className="w-3 h-3" />
              <span>Click to view</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full bg-white">
      {/* Chat area - dynamic width based on panel state */}
      <div 
        className={`flex flex-col h-full transition-all duration-300 ease-out ${
          artifactPanelOpen && isDesktop ? 'border-r border-neutral-200' : ''
        }`}
        style={{
          width: artifactPanelOpen 
            ? (isDesktop ? `calc(100% - ${artifactPanelWidth}px)` : '100%')
            : '100%',
          minWidth: artifactPanelOpen && isDesktop ? '400px' : 'auto' // Increased minimum chat width
        }}
      >
      {/* Messages list */}
      <div ref={listRef} className="overflow-y-auto flex-1">
        <div 
          className={`py-6 mx-auto space-y-6 w-full max-w-3xl sm:py-8 ${
            artifactPanelOpen && isDesktop 
              ? 'px-4 pr-12' // Extra right padding when panel is open
              : 'px-4 sm:px-6'
          }`}
        >
          {messages.length === 0 && (
            <div className="text-sm text-center text-neutral-500">
              Start a conversation with Kavia AI
            </div>
          )}

          {messages.map((m) => {
            const isUser = m.role === 'user';
            return (
              <div key={m.id} className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
                {!isUser && (
                  <div className="mt-1 mr-3 select-none" aria-hidden>
                    {/* Kavia logo */}
                    <div className="grid place-items-center w-8 h-8">
                      <Image src="/kavia-logo.svg" alt="Kavia" width={24} height={24} className="object-contain" />
                    </div>
                  </div>
                )}
                <div
                  className={`max-w-[720px] rounded-2xl px-4 py-3 leading-6 text-[13.5px] sm:text-[14px] shadow-[0px_1px_2px_rgba(0,0,0,0.04)] ${
                    isUser
                      ? 'bg-[#F7F8F9] text-[#191616]'
                      : 'bg-white text-[#191616] border border-black/5'
                  }`}
                  style={{ animation: 'fadeInUp 220ms ease-out' }}
                >
                  {m.content}
                  {/* Show artifact card if message has an artifact */}
                  {m.artifactId && <ArtifactCard artifactId={m.artifactId} />}
                </div>
              </div>
            );
          })}

          {/* Thinking indicator */}
          {isThinking && (
            <div className="flex items-center gap-2 text-[13px] text-neutral-600">
              <div className="mt-1 mr-3 select-none" aria-hidden>
                <div className="grid place-items-center w-8 h-8">
                  <Image src="/kavia-logo.svg" alt="Kavia" width={24} height={24} className="object-contain" />
                </div>
              </div>
              <Spinner size="sm" variant="primary" />
              <span>Thinking…</span>
            </div>
          )}
        </div>
      </div>

      {/* Gradient overlay at bottom to mimic Claude-like polish */}
      <div className="absolute inset-x-0 bottom-24 h-16 bg-gradient-to-t from-white to-transparent pointer-events-none" />

      {/* Inline compact composer pinned to bottom */}
      <div className="sticky bottom-0 z-10 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/65">
        <div 
          className={`py-3 mx-auto w-full max-w-3xl ${
            artifactPanelOpen && isDesktop 
              ? 'px-4 pr-12' // Extra right padding when panel is open to match messages
              : 'px-3 sm:px-4' // Standard padding
          }`}
        >
          <ChatInput
            variant="inline"
            size="sm"
            value={input}
            onChange={setInput}
            onSend={handleSend}
            isLoading={isThinking}
            maxLength={8000}
          />
        </div>
      </div>

      <style jsx global>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(4px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
      </div>

      {/* Artifact Panel */}
      <ArtifactPanel />
    </div>
  );
}
