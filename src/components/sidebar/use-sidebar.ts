'use client';

import { useSidebarStore, useSidebarSections } from '@/store/sidebar';

// Main sidebar hook - replaces the old context-based useSidebar
export function useSidebar() {
  // Individual selectors to avoid creating new objects on every render
  const isOpen = useSidebarStore((state) => state.isOpen);
  const isExpanded = useSidebarStore((state) => state.isExpanded);
  const width = useSidebarStore((state) => state.width);
  const collapsedWidth = useSidebarStore((state) => state.collapsedWidth);
  const sections = useSidebarStore((state) => state.sections);
  const activeItem = useSidebarStore((state) => state.activeItem);
  const isPinned = useSidebarStore((state) => state.isPinned);

  // Actions
  const toggle = useSidebarStore((state) => state.toggle);
  const expand = useSidebarStore((state) => state.expand);
  const collapse = useSidebarStore((state) => state.collapse);
  const open = useSidebarStore((state) => state.open);
  const close = useSidebarStore((state) => state.close);
  const setWidth = useSidebarStore((state) => state.setWidth);
  const setActiveItem = useSidebarStore((state) => state.setActiveItem);
  const togglePin = useSidebarStore((state) => state.togglePin);
  const updateSection = useSidebarStore((state) => state.updateSection);
  const addItem = useSidebarStore((state) => state.addItem);
  const removeItem = useSidebarStore((state) => state.removeItem);

  return {
    state: {
      isOpen,
      isExpanded,
      width,
      collapsedWidth,
      sections,
      activeItem,
      isPinned,
    },
    toggle,
    expand,
    collapse,
    open,
    close,
    setWidth,
    setActiveItem,
    togglePin,
    updateSection,
    addItem,
    removeItem,
  };
}

// Export the sections hook as well
export { useSidebarSections };
