'use client';

import { useSidebarStore, useSidebarSections } from '@/store/sidebar';

// Main sidebar hook - replaces the old context-based useSidebar
export function useSidebar() {
  const state = useSidebarStore((state) => ({
    isOpen: state.isOpen,
    isExpanded: state.isExpanded,
    width: state.width,
    collapsedWidth: state.collapsedWidth,
    sections: state.sections,
    activeItem: state.activeItem,
    isPinned: state.isPinned,
  }));

  const actions = useSidebarStore((state) => ({
    toggle: state.toggle,
    expand: state.expand,
    collapse: state.collapse,
    open: state.open,
    close: state.close,
    setWidth: state.setWidth,
    setActiveItem: state.setActiveItem,
    togglePin: state.togglePin,
    updateSection: state.updateSection,
    addItem: state.addItem,
    removeItem: state.removeItem,
  }));

  return {
    state,
    ...actions,
  };
}

// Export the sections hook as well
export { useSidebarSections };
