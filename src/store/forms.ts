import { create } from 'zustand';

// Generic form field state
interface FormField {
  value: string;
  error?: string;
  touched: boolean;
  loading?: boolean;
}

// Form states for different components
interface FormsState {
  // Chat input form
  chatInput: {
    value: string;
    files: File[];
    attachmentCount: number;
    isLoading: boolean;
  };
  
  // Search forms
  searchForms: {
    [formId: string]: {
      query: string;
      isLoading: boolean;
      results?: any[];
    };
  };
  
  // Generic form fields
  fields: {
    [fieldId: string]: FormField;
  };
  
  // UI states for forms
  ui: {
    [formId: string]: {
      visibleCount?: number;
      openMenuId?: string | null;
      selectedItems?: string[];
    };
  };
}

interface FormsStore extends FormsState {
  // Chat input actions
  setChatInputValue: (value: string) => void;
  setChatInputFiles: (files: File[]) => void;
  setChatInputAttachmentCount: (count: number) => void;
  setChatInputLoading: (loading: boolean) => void;
  resetChatInput: () => void;
  
  // Search form actions
  setSearchQuery: (formId: string, query: string) => void;
  setSearchLoading: (formId: string, loading: boolean) => void;
  setSearchResults: (formId: string, results: any[]) => void;
  clearSearchForm: (formId: string) => void;
  
  // Generic field actions
  setFieldValue: (fieldId: string, value: string) => void;
  setFieldError: (fieldId: string, error?: string) => void;
  setFieldTouched: (fieldId: string, touched: boolean) => void;
  setFieldLoading: (fieldId: string, loading?: boolean) => void;
  clearField: (fieldId: string) => void;
  
  // UI state actions
  setVisibleCount: (formId: string, count: number) => void;
  setOpenMenuId: (formId: string, menuId: string | null) => void;
  setSelectedItems: (formId: string, items: string[]) => void;
  clearFormUI: (formId: string) => void;
}

const initialState: FormsState = {
  chatInput: {
    value: '',
    files: [],
    attachmentCount: 0,
    isLoading: false,
  },
  searchForms: {},
  fields: {},
  ui: {},
};

export const useFormsStore = create<FormsStore>((set, get) => ({
  ...initialState,

  // Chat input actions
  setChatInputValue: (value: string) => {
    set((state) => ({
      chatInput: { ...state.chatInput, value },
    }));
  },

  setChatInputFiles: (files: File[]) => {
    set((state) => ({
      chatInput: { ...state.chatInput, files },
    }));
  },

  setChatInputAttachmentCount: (count: number) => {
    set((state) => ({
      chatInput: { ...state.chatInput, attachmentCount: count },
    }));
  },

  setChatInputLoading: (loading: boolean) => {
    set((state) => ({
      chatInput: { ...state.chatInput, isLoading: loading },
    }));
  },

  resetChatInput: () => {
    set((state) => ({
      chatInput: {
        value: '',
        files: [],
        attachmentCount: 0,
        isLoading: false,
      },
    }));
  },

  // Search form actions
  setSearchQuery: (formId: string, query: string) => {
    set((state) => ({
      searchForms: {
        ...state.searchForms,
        [formId]: {
          ...state.searchForms[formId],
          query,
        },
      },
    }));
  },

  setSearchLoading: (formId: string, loading: boolean) => {
    set((state) => ({
      searchForms: {
        ...state.searchForms,
        [formId]: {
          ...state.searchForms[formId],
          isLoading: loading,
        },
      },
    }));
  },

  setSearchResults: (formId: string, results: any[]) => {
    set((state) => ({
      searchForms: {
        ...state.searchForms,
        [formId]: {
          ...state.searchForms[formId],
          results,
        },
      },
    }));
  },

  clearSearchForm: (formId: string) => {
    set((state) => {
      const newSearchForms = { ...state.searchForms };
      delete newSearchForms[formId];
      return { searchForms: newSearchForms };
    });
  },

  // Generic field actions
  setFieldValue: (fieldId: string, value: string) => {
    set((state) => ({
      fields: {
        ...state.fields,
        [fieldId]: {
          ...state.fields[fieldId],
          value,
          touched: true,
        },
      },
    }));
  },

  setFieldError: (fieldId: string, error?: string) => {
    set((state) => ({
      fields: {
        ...state.fields,
        [fieldId]: {
          ...state.fields[fieldId],
          error,
        },
      },
    }));
  },

  setFieldTouched: (fieldId: string, touched: boolean) => {
    set((state) => ({
      fields: {
        ...state.fields,
        [fieldId]: {
          ...state.fields[fieldId],
          touched,
        },
      },
    }));
  },

  setFieldLoading: (fieldId: string, loading?: boolean) => {
    set((state) => ({
      fields: {
        ...state.fields,
        [fieldId]: {
          ...state.fields[fieldId],
          loading,
        },
      },
    }));
  },

  clearField: (fieldId: string) => {
    set((state) => {
      const newFields = { ...state.fields };
      delete newFields[fieldId];
      return { fields: newFields };
    });
  },

  // UI state actions
  setVisibleCount: (formId: string, count: number) => {
    set((state) => ({
      ui: {
        ...state.ui,
        [formId]: {
          ...state.ui[formId],
          visibleCount: count,
        },
      },
    }));
  },

  setOpenMenuId: (formId: string, menuId: string | null) => {
    set((state) => ({
      ui: {
        ...state.ui,
        [formId]: {
          ...state.ui[formId],
          openMenuId: menuId,
        },
      },
    }));
  },

  setSelectedItems: (formId: string, items: string[]) => {
    set((state) => ({
      ui: {
        ...state.ui,
        [formId]: {
          ...state.ui[formId],
          selectedItems: items,
        },
      },
    }));
  },

  clearFormUI: (formId: string) => {
    set((state) => {
      const newUI = { ...state.ui };
      delete newUI[formId];
      return { ui: newUI };
    });
  },
}));

// Helper hooks for specific form functionality
export const useChatInputForm = () => {
  const chatInput = useFormsStore((state) => state.chatInput);
  const setChatInputValue = useFormsStore((state) => state.setChatInputValue);
  const setChatInputFiles = useFormsStore((state) => state.setChatInputFiles);
  const setChatInputAttachmentCount = useFormsStore((state) => state.setChatInputAttachmentCount);
  const setChatInputLoading = useFormsStore((state) => state.setChatInputLoading);
  const resetChatInput = useFormsStore((state) => state.resetChatInput);

  return {
    ...chatInput,
    setValue: setChatInputValue,
    setFiles: setChatInputFiles,
    setAttachmentCount: setChatInputAttachmentCount,
    setLoading: setChatInputLoading,
    reset: resetChatInput,
  };
};

export const useSearchForm = (formId: string) => {
  const searchForm = useFormsStore((state) => state.searchForms[formId] || { query: '', isLoading: false });
  const setSearchQuery = useFormsStore((state) => state.setSearchQuery);
  const setSearchLoading = useFormsStore((state) => state.setSearchLoading);
  const setSearchResults = useFormsStore((state) => state.setSearchResults);
  const clearSearchForm = useFormsStore((state) => state.clearSearchForm);

  return {
    ...searchForm,
    setQuery: (query: string) => setSearchQuery(formId, query),
    setLoading: (loading: boolean) => setSearchLoading(formId, loading),
    setResults: (results: any[]) => setSearchResults(formId, results),
    clear: () => clearSearchForm(formId),
  };
};

export const useFormUI = (formId: string) => {
  const ui = useFormsStore((state) => state.ui[formId] || {});
  const setVisibleCount = useFormsStore((state) => state.setVisibleCount);
  const setOpenMenuId = useFormsStore((state) => state.setOpenMenuId);
  const setSelectedItems = useFormsStore((state) => state.setSelectedItems);
  const clearFormUI = useFormsStore((state) => state.clearFormUI);

  return {
    ...ui,
    setVisibleCount: (count: number) => setVisibleCount(formId, count),
    setOpenMenuId: (menuId: string | null) => setOpenMenuId(formId, menuId),
    setSelectedItems: (items: string[]) => setSelectedItems(formId, items),
    clear: () => clearFormUI(formId),
  };
};
