import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Tenant } from '@/types';

interface TenantStore {
  // State
  tenant: Tenant | null;
  tenantSlug: string;
  isLoading: boolean;
  error: string | null;

  // Actions
  setTenant: (tenant: Tenant | null, slug: string) => void;
  setTenantSlug: (slug: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearTenant: () => void;
  
  // Computed/Helper methods
  getAPIURL: (path: string) => string;
  isValidTenant: () => boolean;
}

export const useTenantStore = create<TenantStore>()(
  persist(
    (set, get) => ({
      // Initial state
      tenant: null,
      tenantSlug: '',
      isLoading: false,
      error: null,

      // Actions
      setTenant: (tenant: Tenant | null, slug: string) => {
        set({ 
          tenant, 
          tenantSlug: slug,
          error: null 
        });
      },

      setTenantSlug: (slug: string) => {
        set({ tenantSlug: slug });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      clearTenant: () => {
        set({ 
          tenant: null, 
          tenantSlug: '', 
          error: null,
          isLoading: false 
        });
      },

      // Helper methods
      getAPIURL: (path: string) => {
        const { tenantSlug } = get();
        return `/api/${tenantSlug}${path}`;
      },

      isValidTenant: () => {
        const { tenant, tenantSlug } = get();
        return !!(tenant && tenantSlug && tenant.slug === tenantSlug);
      },
    }),
    {
      name: 'kavia-tenant-storage',
      partialize: (state) => ({
        tenant: state.tenant,
        tenantSlug: state.tenantSlug,
      }),
    }
  )
);

// Helper hooks for specific tenant functionality
export const useTenantAPI = () => {
  const getAPIURL = useTenantStore((state) => state.getAPIURL);
  const tenantSlug = useTenantStore((state) => state.tenantSlug);

  return {
    getURL: getAPIURL,
    tenant: tenantSlug,
  };
};

export const useTenantValidation = () => {
  const isValidTenant = useTenantStore((state) => state.isValidTenant);
  const tenant = useTenantStore((state) => state.tenant);
  const tenantSlug = useTenantStore((state) => state.tenantSlug);
  const error = useTenantStore((state) => state.error);

  return {
    isValid: isValidTenant(),
    tenant,
    tenantSlug,
    error,
  };
};
