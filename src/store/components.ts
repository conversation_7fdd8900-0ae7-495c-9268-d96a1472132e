import { create } from 'zustand';

// Component-specific state interfaces
interface ArtifactPanelState {
  activeTab: 'code' | 'preview' | 'reference';
  isMaximized: boolean;
  isResizing: boolean;
  copiedStates: { [key: string]: boolean };
}

interface ChatHistoryState {
  searchQuery: string;
  visibleCount: number;
  isLoading: boolean;
  openMenuId: string | null;
}

interface ProjectsState {
  searchQuery: string;
  visibleCount: number;
  isLoading: boolean;
  openMenuId: string | null;
}

interface ProjectChatHistoryState {
  searchQuery: string;
  visibleCount: number;
  isLoading: boolean;
  openMenuId: string | null;
}

interface ChatViewState {
  input: string;
  isDesktop: boolean;
  showScrollButton: boolean;
}

// Main component store state
interface ComponentsState {
  artifactPanel: ArtifactPanelState;
  chatHistory: ChatHistoryState;
  projects: ProjectsState;
  projectChatHistory: ProjectChatHistoryState;
  chatView: ChatViewState;
}

interface ComponentsStore extends ComponentsState {
  // Artifact Panel actions
  setArtifactPanelTab: (tab: 'code' | 'preview' | 'reference') => void;
  setArtifactPanelMaximized: (maximized: boolean) => void;
  setArtifactPanelResizing: (resizing: boolean) => void;
  setArtifactPanelCopied: (key: string, copied: boolean) => void;
  resetArtifactPanel: () => void;

  // Chat History actions
  setChatHistorySearch: (query: string) => void;
  setChatHistoryVisibleCount: (count: number) => void;
  setChatHistoryLoading: (loading: boolean) => void;
  setChatHistoryOpenMenu: (menuId: string | null) => void;
  resetChatHistory: () => void;

  // Projects actions
  setProjectsSearch: (query: string) => void;
  setProjectsVisibleCount: (count: number) => void;
  setProjectsLoading: (loading: boolean) => void;
  setProjectsOpenMenu: (menuId: string | null) => void;
  resetProjects: () => void;

  // Project Chat History actions
  setProjectChatHistorySearch: (query: string) => void;
  setProjectChatHistoryVisibleCount: (count: number) => void;
  setProjectChatHistoryLoading: (loading: boolean) => void;
  setProjectChatHistoryOpenMenu: (menuId: string | null) => void;
  resetProjectChatHistory: () => void;

  // Chat View actions
  setChatViewInput: (input: string) => void;
  setChatViewDesktop: (isDesktop: boolean) => void;
  setChatViewScrollButton: (show: boolean) => void;
  resetChatView: () => void;
}

const initialState: ComponentsState = {
  artifactPanel: {
    activeTab: 'code',
    isMaximized: false,
    isResizing: false,
    copiedStates: {},
  },
  chatHistory: {
    searchQuery: '',
    visibleCount: 6,
    isLoading: false,
    openMenuId: null,
  },
  projects: {
    searchQuery: '',
    visibleCount: 6,
    isLoading: false,
    openMenuId: null,
  },
  projectChatHistory: {
    searchQuery: '',
    visibleCount: 6,
    isLoading: false,
    openMenuId: null,
  },
  chatView: {
    input: '',
    isDesktop: true,
    showScrollButton: false,
  },
};

export const useComponentsStore = create<ComponentsStore>((set) => ({
  ...initialState,

  // Artifact Panel actions
  setArtifactPanelTab: (tab) => {
    set((state) => ({
      artifactPanel: { ...state.artifactPanel, activeTab: tab },
    }));
  },

  setArtifactPanelMaximized: (maximized) => {
    set((state) => ({
      artifactPanel: { ...state.artifactPanel, isMaximized: maximized },
    }));
  },

  setArtifactPanelResizing: (resizing) => {
    set((state) => ({
      artifactPanel: { ...state.artifactPanel, isResizing: resizing },
    }));
  },

  setArtifactPanelCopied: (key, copied) => {
    set((state) => ({
      artifactPanel: {
        ...state.artifactPanel,
        copiedStates: { ...state.artifactPanel.copiedStates, [key]: copied },
      },
    }));
  },

  resetArtifactPanel: () => {
    set((state) => ({
      artifactPanel: initialState.artifactPanel,
    }));
  },

  // Chat History actions
  setChatHistorySearch: (query) => {
    set((state) => ({
      chatHistory: { ...state.chatHistory, searchQuery: query },
    }));
  },

  setChatHistoryVisibleCount: (count) => {
    set((state) => ({
      chatHistory: { ...state.chatHistory, visibleCount: count },
    }));
  },

  setChatHistoryLoading: (loading) => {
    set((state) => ({
      chatHistory: { ...state.chatHistory, isLoading: loading },
    }));
  },

  setChatHistoryOpenMenu: (menuId) => {
    set((state) => ({
      chatHistory: { ...state.chatHistory, openMenuId: menuId },
    }));
  },

  resetChatHistory: () => {
    set((state) => ({
      chatHistory: initialState.chatHistory,
    }));
  },

  // Projects actions
  setProjectsSearch: (query) => {
    set((state) => ({
      projects: { ...state.projects, searchQuery: query },
    }));
  },

  setProjectsVisibleCount: (count) => {
    set((state) => ({
      projects: { ...state.projects, visibleCount: count },
    }));
  },

  setProjectsLoading: (loading) => {
    set((state) => ({
      projects: { ...state.projects, isLoading: loading },
    }));
  },

  setProjectsOpenMenu: (menuId) => {
    set((state) => ({
      projects: { ...state.projects, openMenuId: menuId },
    }));
  },

  resetProjects: () => {
    set((state) => ({
      projects: initialState.projects,
    }));
  },

  // Project Chat History actions
  setProjectChatHistorySearch: (query) => {
    set((state) => ({
      projectChatHistory: { ...state.projectChatHistory, searchQuery: query },
    }));
  },

  setProjectChatHistoryVisibleCount: (count) => {
    set((state) => ({
      projectChatHistory: { ...state.projectChatHistory, visibleCount: count },
    }));
  },

  setProjectChatHistoryLoading: (loading) => {
    set((state) => ({
      projectChatHistory: { ...state.projectChatHistory, isLoading: loading },
    }));
  },

  setProjectChatHistoryOpenMenu: (menuId) => {
    set((state) => ({
      projectChatHistory: { ...state.projectChatHistory, openMenuId: menuId },
    }));
  },

  resetProjectChatHistory: () => {
    set((state) => ({
      projectChatHistory: initialState.projectChatHistory,
    }));
  },

  // Chat View actions
  setChatViewInput: (input) => {
    set((state) => ({
      chatView: { ...state.chatView, input },
    }));
  },

  setChatViewDesktop: (isDesktop) => {
    set((state) => ({
      chatView: { ...state.chatView, isDesktop },
    }));
  },

  setChatViewScrollButton: (show) => {
    set((state) => ({
      chatView: { ...state.chatView, showScrollButton: show },
    }));
  },

  resetChatView: () => {
    set((state) => ({
      chatView: initialState.chatView,
    }));
  },
}));

// Helper hooks for specific components
export const useArtifactPanel = () => {
  const activeTab = useComponentsStore((state) => state.artifactPanel.activeTab);
  const isMaximized = useComponentsStore((state) => state.artifactPanel.isMaximized);
  const isResizing = useComponentsStore((state) => state.artifactPanel.isResizing);
  const copiedStates = useComponentsStore((state) => state.artifactPanel.copiedStates);
  const setTab = useComponentsStore((state) => state.setArtifactPanelTab);
  const setMaximized = useComponentsStore((state) => state.setArtifactPanelMaximized);
  const setResizing = useComponentsStore((state) => state.setArtifactPanelResizing);
  const setCopied = useComponentsStore((state) => state.setArtifactPanelCopied);
  const reset = useComponentsStore((state) => state.resetArtifactPanel);

  return {
    activeTab,
    isMaximized,
    isResizing,
    copiedStates,
    setTab,
    setMaximized,
    setResizing,
    setCopied,
    reset,
  };
};

export const useChatHistoryComponent = () => {
  const searchQuery = useComponentsStore((state) => state.chatHistory.searchQuery);
  const visibleCount = useComponentsStore((state) => state.chatHistory.visibleCount);
  const isLoading = useComponentsStore((state) => state.chatHistory.isLoading);
  const openMenuId = useComponentsStore((state) => state.chatHistory.openMenuId);
  const setSearch = useComponentsStore((state) => state.setChatHistorySearch);
  const setVisibleCount = useComponentsStore((state) => state.setChatHistoryVisibleCount);
  const setLoading = useComponentsStore((state) => state.setChatHistoryLoading);
  const setOpenMenu = useComponentsStore((state) => state.setChatHistoryOpenMenu);
  const reset = useComponentsStore((state) => state.resetChatHistory);

  return {
    searchQuery,
    visibleCount,
    isLoading,
    openMenuId,
    setSearch,
    setVisibleCount,
    setLoading,
    setOpenMenu,
    reset,
  };
};

export const useProjectsComponent = () => {
  const searchQuery = useComponentsStore((state) => state.projects.searchQuery);
  const visibleCount = useComponentsStore((state) => state.projects.visibleCount);
  const isLoading = useComponentsStore((state) => state.projects.isLoading);
  const openMenuId = useComponentsStore((state) => state.projects.openMenuId);
  const setSearch = useComponentsStore((state) => state.setProjectsSearch);
  const setVisibleCount = useComponentsStore((state) => state.setProjectsVisibleCount);
  const setLoading = useComponentsStore((state) => state.setProjectsLoading);
  const setOpenMenu = useComponentsStore((state) => state.setProjectsOpenMenu);
  const reset = useComponentsStore((state) => state.resetProjects);

  return {
    searchQuery,
    visibleCount,
    isLoading,
    openMenuId,
    setSearch,
    setVisibleCount,
    setLoading,
    setOpenMenu,
    reset,
  };
};

export const useProjectChatHistoryComponent = () => {
  const searchQuery = useComponentsStore((state) => state.projectChatHistory.searchQuery);
  const visibleCount = useComponentsStore((state) => state.projectChatHistory.visibleCount);
  const isLoading = useComponentsStore((state) => state.projectChatHistory.isLoading);
  const openMenuId = useComponentsStore((state) => state.projectChatHistory.openMenuId);
  const setSearch = useComponentsStore((state) => state.setProjectChatHistorySearch);
  const setVisibleCount = useComponentsStore((state) => state.setProjectChatHistoryVisibleCount);
  const setLoading = useComponentsStore((state) => state.setProjectChatHistoryLoading);
  const setOpenMenu = useComponentsStore((state) => state.setProjectChatHistoryOpenMenu);
  const reset = useComponentsStore((state) => state.resetProjectChatHistory);

  return {
    searchQuery,
    visibleCount,
    isLoading,
    openMenuId,
    setSearch,
    setVisibleCount,
    setLoading,
    setOpenMenu,
    reset,
  };
};

export const useChatViewComponent = () => {
  const input = useComponentsStore((state) => state.chatView.input);
  const isDesktop = useComponentsStore((state) => state.chatView.isDesktop);
  const showScrollButton = useComponentsStore((state) => state.chatView.showScrollButton);
  const setInput = useComponentsStore((state) => state.setChatViewInput);
  const setDesktop = useComponentsStore((state) => state.setChatViewDesktop);
  const setScrollButton = useComponentsStore((state) => state.setChatViewScrollButton);
  const reset = useComponentsStore((state) => state.resetChatView);

  return {
    input,
    isDesktop,
    showScrollButton,
    setInput,
    setDesktop,
    setScrollButton,
    reset,
  };
};
