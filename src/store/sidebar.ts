import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { SidebarState, SidebarSection, SidebarItem } from '@/types';

interface SidebarStore extends SidebarState {
  // Actions
  toggle: () => void;
  expand: () => void;
  collapse: () => void;
  open: () => void;
  close: () => void;
  setWidth: (width: number) => void;
  setActiveItem: (itemId: string) => void;
  togglePin: () => void;
  updateSection: (sectionId: string, updates: Partial<SidebarSection>) => void;
  addItem: (sectionId: string, item: SidebarItem, index?: number) => void;
  removeItem: (sectionId: string, itemId: string) => void;
  setSections: (sections: SidebarSection[]) => void;
  loadState: (state: Partial<SidebarState>) => void;
}

const initialState: SidebarState = {
  isOpen: true,
  isExpanded: false,
  width: 280,
  collapsedWidth: 60,
  sections: [],
  activeItem: undefined,
  isPinned: false,
};

export const useSidebarStore = create<SidebarStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      toggle: () => {
        set((state) => ({ isOpen: !state.isOpen }));
      },

      expand: () => {
        set({ isExpanded: true });
      },

      collapse: () => {
        set({ isExpanded: false });
      },

      open: () => {
        set({ isOpen: true });
      },

      close: () => {
        set({ isOpen: false });
      },

      setWidth: (width: number) => {
        set({ width });
      },

      setActiveItem: (itemId: string) => {
        set({ activeItem: itemId });
      },

      togglePin: () => {
        set((state) => ({ isPinned: !state.isPinned }));
      },

      updateSection: (sectionId: string, updates: Partial<SidebarSection>) => {
        set((state) => ({
          sections: state.sections.map((section) =>
            section.id === sectionId ? { ...section, ...updates } : section
          ),
        }));
      },

      addItem: (sectionId: string, item: SidebarItem, index?: number) => {
        set((state) => ({
          sections: state.sections.map((section) => {
            if (section.id === sectionId) {
              const newItems = [...section.items];
              if (index !== undefined) {
                newItems.splice(index, 0, item);
              } else {
                newItems.push(item);
              }
              return { ...section, items: newItems };
            }
            return section;
          }),
        }));
      },

      removeItem: (sectionId: string, itemId: string) => {
        set((state) => ({
          sections: state.sections.map((section) => {
            if (section.id === sectionId) {
              return {
                ...section,
                items: section.items.filter((item) => item.id !== itemId),
              };
            }
            return section;
          }),
        }));
      },

      setSections: (sections: SidebarSection[]) => {
        set({ sections });
      },

      loadState: (newState: Partial<SidebarState>) => {
        set((state) => ({ ...state, ...newState }));
      },
    }),
    {
      name: 'kavia-sidebar-storage',
      partialize: (state) => ({
        isOpen: state.isOpen,
        isExpanded: state.isExpanded,
        width: state.width,
        isPinned: state.isPinned,
        activeItem: state.activeItem,
      }),
    }
  )
);

// Helper hooks for specific sidebar functionality
export const useSidebarSections = () => {
  const sections = useSidebarStore((state) => state.sections);
  const updateSection = useSidebarStore((state) => state.updateSection);
  const addItem = useSidebarStore((state) => state.addItem);
  const removeItem = useSidebarStore((state) => state.removeItem);

  return {
    sections,
    updateSection,
    addItem,
    removeItem,
    findSection: (sectionId: string) => 
      sections.find(section => section.id === sectionId),
    findItem: (itemId: string) => {
      for (const section of sections) {
        const item = section.items.find(item => item.id === itemId);
        if (item) return { section, item };
      }
      return null;
    },
  };
};
