import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UIState, Notification } from '@/types';

type Theme = 'light' | 'dark' | 'system';

interface UIStore extends UIState {
  // Theme state
  actualTheme: 'light' | 'dark';

  // Actions
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setActiveTab: (tab: string) => void;
  setTheme: (theme: Theme) => void;
  updateActualTheme: () => void;
  addNotification: (
    notification: Omit<Notification, 'id' | 'createdAt' | 'read'>
  ) => void;
  removeNotification: (id: string) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
}

// Helper function to resolve actual theme
const resolveActualTheme = (theme: Theme): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';

  if (theme === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches
      ? 'dark'
      : 'light';
  }
  return theme;
};

export const useUIStore = create<UIStore>()(
  persist(
    (set, get) => ({
      sidebarOpen: false,
      activeTab: 'chat',
      theme: 'system',
      actualTheme: 'light',
      notifications: [],

      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open });
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },

      setActiveTab: (tab: string) => {
        set({ activeTab: tab });
      },

      setTheme: (theme: Theme) => {
        const actualTheme = resolveActualTheme(theme);
        set({ theme, actualTheme });

        // Update DOM classes
        if (typeof window !== 'undefined') {
          const root = document.documentElement;
          if (actualTheme === 'dark') {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      updateActualTheme: () => {
        const { theme } = get();
        const actualTheme = resolveActualTheme(theme);
        set({ actualTheme });

        // Update DOM classes
        if (typeof window !== 'undefined') {
          const root = document.documentElement;
          if (actualTheme === 'dark') {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      addNotification: (notificationData) => {
        const notification: Notification = {
          ...notificationData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          read: false,
        };

        set((state) => ({
          notifications: [notification, ...state.notifications],
        }));
      },

      removeNotification: (id: string) => {
        set((state) => ({
          notifications: state.notifications.filter((n) => n.id !== id),
        }));
      },

      markNotificationRead: (id: string) => {
        set((state) => ({
          notifications: state.notifications.map((n) =>
            n.id === id ? { ...n, read: true } : n
          ),
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },
    }),
    {
      name: 'kavia-ui-storage',
      partialize: (state) => ({
        theme: state.theme,
        sidebarOpen: state.sidebarOpen,
        activeTab: state.activeTab,
      }),
    }
  )
);
